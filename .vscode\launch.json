{"version": "0.2.0", "configurations": [{"name": "GoLaunch", "type": "go", "request": "launch", "mode": "debug", "remotePath": "", "port": 2345, "host": "127.0.0.1", "program": "${fileDirname}", "env": {"GOPATH": "D:\\GoWork"}, "args": [], "showLog": true}, {"name": "Launch Chrome", "type": "chrome", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}"}, {"name": "Launch Firefox", "type": "firefox", "request": "launch", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}"}]}