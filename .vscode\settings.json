{
    // Editor settings
    "editor.columnSelection": true,
    "editor.unicodeHighlight.nonBasicASCII": false,
    "editor.quickSuggestions": {
        "other": true,
        "comments": false,
        "strings": true
    },

    // Terminal settings
    "terminal.integrated.shellIntegration.enabled": true,

    // Workbench settings
    "workbench.colorTheme": "Visual Studio Light",

    // Git settings
    "git.confirmSync": false,

    // Remote SSH settings
    "remote.SSH.remotePlatform": {
        "**************": "linux"
    },

    // File settings
    "files.autoSave": "onFocusChange",

    // Debug settings
    "debug.openDebug": "openOnDebugBreak",
    "debug.internalConsoleOptions": "openOnSessionStart",

    // Go language settings
    "go.buildOnSave": "workspace",
    "go.lintOnSave": "package",
    "go.vetOnSave": "package",
    "go.buildTags": "",
    "go.buildFlags": [],
    "go.lintFlags": [],
    "go.vetFlags": [],
    "go.coverOnSave": false,
    "go.useCodeSnippetsOnFunctionSuggest": false,
    "go.formatOnSave": true,
    "go.formatTool": "goreturns",
    "go.goroot": "D:\\Program Files\\Go1.19.4\\GoRoot",
    "go.gopath": "D:\\GoWork",
    "go.gocodeAutoBuild": false,

    // Dart language settings
    "[dart]": {
        "editor.formatOnSave": true,
        "editor.formatOnType": true,
        "editor.rulers": [80],
        "editor.selectionHighlight": false,
        "editor.suggest.snippetsPreventQuickSuggestions": false,
        "editor.suggestSelection": "first",
        "editor.tabCompletion": "onlySnippets",
        "editor.wordBasedSuggestions": "off"
    },

    // Web development settings
    "[html]": {
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "vscode.html-language-features"
    },

    "[css]": {
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "vscode.css-language-features"
    },

    "[javascript]": {
        "editor.formatOnSave": true,
        "editor.defaultFormatter": "vscode.typescript-language-features"
    },

    "[go]": {
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.organizeImports": "explicit"
        }
    }
}
