# 3D特效卡片轮播 - 美女主题分类

一个具有3D轮播效果的特效卡片系统，支持Unsplash API集成。

## 🎨 主题分类

- **清纯型**: 自然清新，素颜或淡妆
- **性感型**: 身材火辣，打扮成熟  
- **可爱型**: 甜美活泼，萌系风格
- **气质型**: 优雅知性，举止端庄
- **古典型**: 传统美感，东方韵味

## ✨ 功能特点

- 🎭 3D轮播展示效果
- 🖼️ 支持Unsplash高质量图片
- 📱 响应式设计
- 🎯 点击卡片查看更多图片
- ⌨️ 键盘控制支持
- 🔄 自动播放功能
- 🎪 悬停翻转效果

## 🚀 快速开始

### 1. 设置Unsplash API

1. 访问 [Unsplash Developers](https://unsplash.com/developers)
2. 注册开发者账号并创建新应用
3. 选择 "Demo" 类型（每小时50次请求）
4. 复制 Access Key

### 2. 配置API密钥

编辑 `config.js` 文件：

```javascript
window.UNSPLASH_CONFIG = {
    ACCESS_KEY: '你的_API密钥_这里', // 替换为实际的Access Key
    API_URL: 'https://api.unsplash.com'
};
```

### 3. 运行项目

直接在浏览器中打开 `index.html` 文件即可。

## 📁 文件结构

```
special-effect-card/
├── index.html      # 主页面
├── styles.css      # 样式文件
├── script.js       # 主要逻辑
├── config.js       # API配置
└── README.md       # 说明文档
```

## 🎮 使用方法

- **左右按钮**: 切换卡片
- **方向键**: ← → 控制卡片切换
- **点击卡片**: 切换到该卡片或打开画廊
- **ESC键**: 关闭画廊
- **鼠标悬停**: 暂停自动播放

## 🔧 技术栈

- HTML5
- CSS3 (3D Transform, Animation)
- JavaScript (ES6+)
- Unsplash API
- Responsive Design

## 📝 API限制

- **Demo版本**: 每小时50次请求
- **生产版本**: 需要申请更高配额

## 🚨 注意事项

1. 请勿将API密钥提交到公开仓库
2. 遵守Unsplash的使用条款
3. 如无API密钥，将使用默认图片服务

## 🔄 更新日志

- v1.0: 基础3D轮播功能
- v1.1: 集成Unsplash API
- v1.2: 美女主题分类优化

## 📄 许可证

本项目仅供学习和演示使用。
