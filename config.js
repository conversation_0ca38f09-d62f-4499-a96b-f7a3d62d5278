// Unsplash API 配置文件
// 请在此文件中设置您的 Unsplash API 密钥

// 步骤1：访问 https://unsplash.com/developers 注册开发者账号
// 步骤2：创建新的应用程序
// 步骤3：获取 Access Key
// 步骤4：将下面的 YOUR_API_KEY_HERE 替换为您的实际API密钥

window.UNSPLASH_CONFIG = {
    ACCESS_KEY: '*******************************************', // 请替换为您的Unsplash Access Key
    API_URL: 'https://api.unsplash.com'
};

// 使用说明：
// 1. 注册Unsplash开发者账号：https://unsplash.com/developers
// 2. 创建新应用，选择 "Demo" 类型即可
// 3. 复制 "Access Key" 到上面的 ACCESS_KEY 字段
// 4. 保存文件并刷新页面

// 注意：
// - Demo应用每小时限制50次请求
// - 如需更多请求，请申请生产环境密钥
// - 请勿将API密钥公开到代码仓库中
