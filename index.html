<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="format-detection" content="telephone=no">
    <title>美女风格卡片轮播</title>
    <link rel="stylesheet" href="styles.css">
    <script src="config.js"></script>
</head>
<body>
    <div class="carousel-container">
        <div class="carousel" id="carousel">
            <!-- 卡片包装器 -->
            <div class="card-wrapper">
                <div class="card">
                    <div class="card-face card-front">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h2 class="card-title">清纯型</h2>
                            <p class="card-description">自然清新，素颜或淡妆</p>
                        </div>
                    </div>
                    <div class="card-face card-back">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h3 class="back-title">风格特色</h3>
                            <ul class="feature-list">
                                <li>清新自然的气质</li>
                                <li>简约而不简单</li>
                                <li>纯真无邪的笑容</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-wrapper">
                <div class="card">
                    <div class="card-face card-front">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h2 class="card-title">性感型</h2>
                            <p class="card-description">身材火辣，打扮成熟</p>
                        </div>
                    </div>
                    <div class="card-face card-back">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h3 class="back-title">风格特色</h3>
                            <ul class="feature-list">
                                <li>惾人的身材曲线</li>
                                <li>成熟魅力的气质</li>
                                <li>自信独立的态度</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-wrapper">
                <div class="card">
                    <div class="card-face card-front">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h2 class="card-title">可爱型</h2>
                            <p class="card-description">甜美活泼，萱系风格</p>
                        </div>
                    </div>
                    <div class="card-face card-back">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h3 class="back-title">风格特色</h3>
                            <ul class="feature-list">
                                <li>甜美可人的外表</li>
                                <li>活泼开朗的性格</li>
                                <li>萱萱的表情动作</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-wrapper">
                <div class="card">
                    <div class="card-face card-front">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h2 class="card-title">气质型</h2>
                            <p class="card-description">优雅知性，举止端庄</p>
                        </div>
                    </div>
                    <div class="card-face card-back">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h3 class="back-title">风格特色</h3>
                            <ul class="feature-list">
                                <li>优雅的举止仪态</li>
                                <li>知性的气质魅力</li>
                                <li>深度与内涵并存</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-wrapper">
                <div class="card">
                    <div class="card-face card-front">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h2 class="card-title">古典型</h2>
                            <p class="card-description">传统美感，东方韵味</p>
                        </div>
                    </div>
                    <div class="card-face card-back">
                        <div class="card-glow"></div>
                        <div class="card-content">
                            <h3 class="back-title">风格特色</h3>
                            <ul class="feature-list">
                                <li>古典传统的美感</li>
                                <li>东方文化的韵味</li>
                                <li>时间沉淀的魅力</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="navigation">
            <button class="nav-prev">⟵ Prev</button>
            <button class="nav-next">Next ⟶</button>
        </div>
    </div>

    <!-- 图片画廊模态框 -->
    <div class="modal" id="galleryModal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2 class="modal-title" id="modalTitle">Gallery</h2>
            <div class="gallery-container">
                <button class="gallery-nav gallery-prev" id="galleryPrev">‹</button>
                <img class="gallery-image" id="galleryImage" src="" alt="Gallery Image">
                <button class="gallery-nav gallery-next" id="galleryNext">›</button>
                
                <!-- 图片操作工具栏 -->
                <div class="image-toolbar">
                    <button class="toolbar-btn" id="viewFullSize" title="查看原图">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M21 3H3c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM5 17l3.5-4.5 2.5 3.01L14.5 11l4.5 6H5z"/>
                        </svg>
                        <span>原图</span>
                    </button>
                    <button class="toolbar-btn" id="setWallpaper" title="设为桌面壁纸">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M4 4h7V2H4c-1.1 0-2 .9-2 2v7h2V4zm6 9l-4 5h12l-3-4-2.03 2.71L10 13zm7-4.5c0-.83-.67-1.5-1.5-1.5S14 7.67 14 8.5s.67 1.5 1.5 1.5S17 9.33 17 8.5zM20 2h-7v2h7v7h2V4c0-1.1-.9-2-2-2zm0 18h-7v2h7c1.1 0 2-.9 2-2v-7h-2v7zM4 13H2v7c0 1.1.9 2 2 2h7v-2H4v-7z"/>
                        </svg>
                        <span>壁纸</span>
                    </button>
                    <button class="toolbar-btn" id="downloadImage" title="下载图片">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                        </svg>
                        <span>下载</span>
                    </button>
                </div>
                
                <!-- 图片评价和统计 -->
                <div class="image-stats">
                    <div class="rating-section">
                        <button class="rating-btn like-btn" id="likeBtn" title="点赞">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M1 21h4V9H1v12zm22-11c0-1.1-.9-2-2-2h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 1 7.59 7.59C7.22 7.95 7 8.45 7 9v10c0 1.1.9 2 2 2h9c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73v-1.91l-.01-.01L23 10z"/>
                            </svg>
                            <span id="likeCount">0</span>
                        </button>
                        <button class="rating-btn dislike-btn" id="dislikeBtn" title="点踩">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15 3H6c-.83 0-1.54.5-1.84 1.22l-3.02 7.05c-.09.23-.14.47-.14.73v1.91l.01.01L1 14c0 1.1.9 2 2 2h6.31l-.95 4.57-.03.32c0 .41.17.79.44 1.06L9.83 23l6.59-6.59c.36-.36.58-.86.58-1.41V5c0-1.1-.9-2-2-2zm4 0v12h4V3h-4z"/>
                            </svg>
                            <span id="dislikeCount">0</span>
                        </button>
                    </div>
                    <div class="download-stats">
                        <span class="stats-label">下载次数:</span>
                        <span id="downloadCount">0</span>
                    </div>
                </div>
            </div>
            <div class="gallery-indicators" id="galleryIndicators"></div>
        </div>
    </div>
    
    <!-- 全屏图片查看器 -->
    <div class="fullscreen-viewer" id="fullscreenViewer">
        <div class="fullscreen-content">
            <button class="fullscreen-close" id="fullscreenClose">&times;</button>
            <img class="fullscreen-image" id="fullscreenImage" src="" alt="Full Size Image">
            <div class="fullscreen-controls">
                <button class="fullscreen-btn" id="zoomIn" title="放大">+</button>
                <button class="fullscreen-btn" id="zoomOut" title="缩小">-</button>
                <button class="fullscreen-btn" id="resetZoom" title="重置">1:1</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
