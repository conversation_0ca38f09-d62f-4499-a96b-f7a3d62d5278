<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特效卡片</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- 粒子背景容器 -->
        <div class="particles" id="particles"></div>
        
        <!-- 卡片容器 -->
        <div class="card-container" id="cardContainer">
            <div class="card" id="card">
                <!-- 卡片正面 -->
                <div class="card-face card-front">
                    <div class="card-glow"></div>
                    <div class="card-content">
                        <h2 class="card-title">特效卡片</h2>
                        <div class="card-divider"></div>
                        <p class="card-description">
                            悬停翻转 · 点击震动
                        </p>
                        <div class="card-stats">
                            <div class="stat">
                                <span class="stat-value">3D</span>
                                <span class="stat-label">效果</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">∞</span>
                                <span class="stat-label">创意</span>
                            </div>
                            <div class="stat">
                                <span class="stat-value">100%</span>
                                <span class="stat-label">响应</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 卡片背面 -->
                <div class="card-face card-back">
                    <div class="card-glow"></div>
                    <div class="card-content">
                        <h3 class="back-title">技术特性</h3>
                        <ul class="feature-list">
                            <li>CSS 3D Transform</li>
                            <li>动态渐变背景</li>
                            <li>粒子动画效果</li>
                            <li>交互式视差</li>
                            <li>响应式设计</li>
                        </ul>
                        <div class="back-footer">
                            <span class="version">v1.0</span>
                            <span class="author">AI Assistant</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
