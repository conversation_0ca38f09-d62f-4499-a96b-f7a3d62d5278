// 从配置文件获取Unsplash API配置
const UNSPLASH_ACCESS_KEY = window.UNSPLASH_CONFIG ? window.UNSPLASH_CONFIG.ACCESS_KEY : 'YOUR_API_KEY_HERE';
const UNSPLASH_API_URL = window.UNSPLASH_CONFIG ? window.UNSPLASH_CONFIG.API_URL : 'https://api.unsplash.com';

// 分类关键词定义 - 优化为美女主题
const categoryKeywords = {
    0: 'beautiful woman portrait natural clean fresh model', // 清纯型
    1: 'sexy woman glamour portrait elegant beautiful model', // 性感型
    2: 'cute girl sweet woman beautiful portrait model', // 可爱型
    3: 'elegant woman business portrait professional beautiful model', // 气质型
    4: 'vintage woman portrait classic beauty traditional model' // 古典型
};

// 图片缓存和分页管理
const imageCache = {
    backgrounds: {},
    galleries: {},
    pages: {}, // 记录每个分类的当前页码
    loadedIds: {}, // 记录已加载的图片ID，避免重复
    loading: {}, // 记录正在加载的状态
    imageData: {} // 存储完整的图片数据（包含统计信息）
};

// 图片统计数据
const imageStats = {
    likes: {},
    dislikes: {},
    downloads: {},
    userActions: {} // 记录用户对每张图片的操作
};

// 初始化分页数据
for (let i = 0; i < 5; i++) {
    imageCache.pages[i] = 1;
    imageCache.loadedIds[i] = new Set();
    imageCache.galleries[i] = [];
    imageCache.loading[i] = false;
}

// 获取元素
const carousel = document.getElementById('carousel');
const prevBtn = document.querySelector('.nav-prev');
const nextBtn = document.querySelector('.nav-next');
const cards = document.querySelectorAll('.card-wrapper');

// 轮播状态
let currentIndex = 0;
const totalCards = cards.length;
const angleStep = 360 / totalCards;

// 初始化卡片位置
function initCarousel() {
    cards.forEach((card, index) => {
        const angle = angleStep * index;
        const rotateY = `rotateY(${angle}deg)`;
        const translateZ = `translateZ(${350}px)`;
        card.style.transform = `${rotateY} ${translateZ}`;
        
        // 设置初始透明度
        if (index === 0) {
            card.style.opacity = '1';
            card.style.transform += ' scale(1.1)';
        } else {
            card.style.opacity = '0.6';
        }
    });
}

// 旋转轮播
function rotateCarousel() {
    const rotation = -currentIndex * angleStep;
    carousel.style.transform = `rotateY(${rotation}deg)`;
    
    // 更新卡片状态
    cards.forEach((card, index) => {
        if (index === currentIndex) {
            card.style.opacity = '1';
            card.style.transform = card.style.transform.replace(/scale\([^)]*\)/, '') + ' scale(1.1)';
        } else {
            card.style.opacity = '0.6';
            card.style.transform = card.style.transform.replace(/scale\([^)]*\)/, '');
        }
    });
}

// 下一张
function nextCard() {
    currentIndex = (currentIndex + 1) % totalCards;
    rotateCarousel();
}

// 上一张
function prevCard() {
    currentIndex = (currentIndex - 1 + totalCards) % totalCards;
    rotateCarousel();
}

// 绑定事件
prevBtn.addEventListener('click', prevCard);
nextBtn.addEventListener('click', nextCard);

// 键盘控制
document.addEventListener('keydown', (e) => {
    if (e.key === 'ArrowLeft') prevCard();
    if (e.key === 'ArrowRight') nextCard();
});

// 自动播放
let autoPlayInterval;
function startAutoPlay() {
    autoPlayInterval = setInterval(nextCard, 3000);
}

function stopAutoPlay() {
    clearInterval(autoPlayInterval);
}

// 鼠标悬停时暂停自动播放
carousel.addEventListener('mouseenter', stopAutoPlay);
carousel.addEventListener('mouseleave', startAutoPlay);

// Unsplash API调用函数 - 支持分页
async function fetchUnsplashImages(query, count = 12, page = 1) {
    try {
        const response = await fetch(
            `${UNSPLASH_API_URL}/search/photos?query=${encodeURIComponent(query)}&per_page=${count}&page=${page}&orientation=landscape`,
            {
                headers: {
                    'Authorization': `Client-ID ${UNSPLASH_ACCESS_KEY}`
                }
            }
        );
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return {
            images: data.results.map(photo => ({
                regular: photo.urls.regular,
                small: photo.urls.small,
                thumb: photo.urls.thumb,
                alt: photo.alt_description || 'Beautiful portrait',
                id: photo.id
            })),
            totalPages: data.total_pages,
            currentPage: page
        };
    } catch (error) {
        console.error('Error fetching images from Unsplash:', error);
        return { images: [], totalPages: 0, currentPage: page };
    }
}

// 为特定分类加载更多图片
async function loadMoreImagesForCategory(categoryId) {
    if (imageCache.loading[categoryId]) {
        console.log(`分类 ${categoryId} 正在加载中...`);
        return;
    }
    
    imageCache.loading[categoryId] = true;
    const keyword = categoryKeywords[categoryId];
    const nextPage = imageCache.pages[categoryId] + 1;
    
    console.log(`为分类 ${categoryId} 加载第 ${nextPage} 页图片...`);
    
    try {
        const result = await fetchUnsplashImages(keyword, 12, nextPage);
        
        if (result.images.length > 0) {
            // 过滤重复图片
            const newImages = result.images.filter(img => 
                !imageCache.loadedIds[categoryId].has(img.id)
            );
            
            // 添加新图片到缓存
            newImages.forEach(img => {
                imageCache.galleries[categoryId].push(img.regular);
                imageCache.loadedIds[categoryId].add(img.id);
            });
            
            // 更新页码
            imageCache.pages[categoryId] = nextPage;
            
            console.log(`分类 ${categoryId} 新增 ${newImages.length} 张图片，总计 ${imageCache.galleries[categoryId].length} 张`);
            
            return newImages.length;
        } else {
            console.warn(`分类 ${categoryId} 第 ${nextPage} 页没有更多图片`);
            return 0;
        }
    } catch (error) {
        console.error(`分类 ${categoryId} 加载失败:`, error);
        return 0;
    } finally {
        imageCache.loading[categoryId] = false;
    }
}

// 加载所有分类的初始图片
async function loadAllCategoryImages() {
    console.log('开始加载各分类初始图片...');
    
    for (let categoryId = 0; categoryId < 5; categoryId++) {
        const keyword = categoryKeywords[categoryId];
        console.log(`加载分类 ${categoryId}: ${keyword}`);
        
        try {
            const result = await fetchUnsplashImages(keyword, 15, 1);
            
            if (result.images.length > 0) {
                // 第一张作为背景图
                imageCache.backgrounds[categoryId] = result.images[0].regular;
                
                // 所有图片作为画廊图片
                imageCache.galleries[categoryId] = result.images.map(img => {
                    imageCache.loadedIds[categoryId].add(img.id);
                    return img.regular;
                });
                
                // 更新卡片背景
                updateCardBackground(categoryId, result.images[0].regular);
                
                console.log(`分类 ${categoryId} 初始加载成功: ${result.images.length} 张图片`);
            } else {
                console.warn(`分类 ${categoryId} 未获取到图片`);
            }
        } catch (error) {
            console.error(`分类 ${categoryId} 加载失败:`, error);
        }
        
        // 防止API调用过于频縁
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('所有分类初始图片加载完成');
}

// 更新卡片背景图片
function updateCardBackground(categoryId, imageUrl) {
    const cardWrapper = document.querySelector(`.card-wrapper:nth-child(${categoryId + 1})`);
    if (cardWrapper) {
        const cardFront = cardWrapper.querySelector('.card-front');
        if (cardFront) {
            // 获取原有的渐变蒙版颜色（从CSS中定义的不同主题色彩）
            let gradient;
            switch(categoryId) {
                case 0: // 清纯型 - 淡蓝色/白色系
                    gradient = 'linear-gradient(rgba(135, 206, 250, 0.6), rgba(176, 224, 230, 0.6))';
                    break;
                case 1: // 性感型 - 红色/黑色系
                    gradient = 'linear-gradient(rgba(220, 20, 60, 0.7), rgba(139, 0, 0, 0.7))';
                    break;
                case 2: // 可爱型 - 粉色/浅色系
                    gradient = 'linear-gradient(rgba(255, 182, 193, 0.6), rgba(255, 228, 225, 0.6))';
                    break;
                case 3: // 气质型 - 紫色/灰色系
                    gradient = 'linear-gradient(rgba(138, 43, 226, 0.6), rgba(147, 112, 219, 0.6))';
                    break;
                case 4: // 古典型 - 金色/棕色系
                    gradient = 'linear-gradient(rgba(218, 165, 32, 0.6), rgba(205, 133, 63, 0.6))';
                    break;
                default:
                    gradient = 'linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.5))';
            }
            
            // 设置背景图片，渐变蒙版在上层，美女图片在下层
            cardFront.style.backgroundImage = `${gradient}, url('${imageUrl}')`;
            
            console.log(`分类 ${categoryId} 卡片背景已更新: ${imageUrl}`);
        }
    }
}

// 初始化
initCarousel();
startAutoPlay();

// 默认美女图片配置 - 按分类主题
const defaultBeautyImages = {
    0: [ // 清纯型
        'https://picsum.photos/800/600?random=1101', // 清纯自然风格
        'https://picsum.photos/800/600?random=1102',
        'https://picsum.photos/800/600?random=1103',
        'https://picsum.photos/800/600?random=1104',
        'https://picsum.photos/800/600?random=1105'
    ],
    1: [ // 性感型
        'https://picsum.photos/800/600?random=2101', // 性感魅力风格
        'https://picsum.photos/800/600?random=2102',
        'https://picsum.photos/800/600?random=2103',
        'https://picsum.photos/800/600?random=2104',
        'https://picsum.photos/800/600?random=2105'
    ],
    2: [ // 可爱型
        'https://picsum.photos/800/600?random=3101', // 甜美可爱风格
        'https://picsum.photos/800/600?random=3102',
        'https://picsum.photos/800/600?random=3103',
        'https://picsum.photos/800/600?random=3104',
        'https://picsum.photos/800/600?random=3105'
    ],
    3: [ // 气质型
        'https://picsum.photos/800/600?random=4101', // 优雅气质风格
        'https://picsum.photos/800/600?random=4102',
        'https://picsum.photos/800/600?random=4103',
        'https://picsum.photos/800/600?random=4104',
        'https://picsum.photos/800/600?random=4105'
    ],
    4: [ // 古典型
        'https://picsum.photos/800/600?random=5101', // 复古古典风格
        'https://picsum.photos/800/600?random=5102',
        'https://picsum.photos/800/600?random=5103',
        'https://picsum.photos/800/600?random=5104',
        'https://picsum.photos/800/600?random=5105'
    ]
};

// 加载默认图片到卡片背景
function loadDefaultImages() {
    console.log('使用默认图片服务，为各分类卡片设置背景...');
    
    for (let categoryId = 0; categoryId < 5; categoryId++) {
        // 使用默认图片的第一张作为卡片背景
        const defaultImage = defaultBeautyImages[categoryId][0];
        updateCardBackground(categoryId, defaultImage);
        
        // 更新画廊缓存，使用默认图片
        imageCache.galleries[categoryId] = [...defaultBeautyImages[categoryId]];
        imageCache.backgrounds[categoryId] = defaultImage;
        
        console.log(`分类 ${categoryId} 已设置默认背景图片`);
    }
    
    console.log('所有分类默认图片加载完成');
}

// 加载图片
// 注意：请在config.js中设置您的 Unsplash API 密钥
if (UNSPLASH_ACCESS_KEY && UNSPLASH_ACCESS_KEY !== 'YOUR_API_KEY_HERE') {
    loadAllCategoryImages();
} else {
    console.warn('请在 config.js 中设置您的 Unsplash API 密钥');
    console.log('使用默认图片服务');
    loadDefaultImages();
}

// 图片画廊数据
const galleries = {
    0: { // 清纯型
        title: '清纯型画廊',
        images: [
            'https://picsum.photos/1600/900?random=1001', // 清纯风格
            'https://picsum.photos/1600/900?random=1002',
            'https://picsum.photos/1600/900?random=1003',
            'https://picsum.photos/1600/900?random=1004',
            'https://picsum.photos/1600/900?random=1005'
        ]
    },
    1: { // 性感型
        title: '性感型画廊',
        images: [
            'https://picsum.photos/1600/900?random=2001', // 性感风格
            'https://picsum.photos/1600/900?random=2002',
            'https://picsum.photos/1600/900?random=2003',
            'https://picsum.photos/1600/900?random=2004',
            'https://picsum.photos/1600/900?random=2005'
        ]
    },
    2: { // 可爱型
        title: '可爱型画廊',
        images: [
            'https://picsum.photos/1600/900?random=3001', // 可爱风格
            'https://picsum.photos/1600/900?random=3002',
            'https://picsum.photos/1600/900?random=3003',
            'https://picsum.photos/1600/900?random=3004',
            'https://picsum.photos/1600/900?random=3005'
        ]
    },
    3: { // 气质型
        title: '气质型画廊',
        images: [
            'https://picsum.photos/1600/900?random=4001', // 气质风格
            'https://picsum.photos/1600/900?random=4002',
            'https://picsum.photos/1600/900?random=4003',
            'https://picsum.photos/1600/900?random=4004',
            'https://picsum.photos/1600/900?random=4005'
        ]
    },
    4: { // 古典型
        title: '古典型画廊',
        images: [
            'https://picsum.photos/1600/900?random=5001', // 古典风格
            'https://picsum.photos/1600/900?random=5002',
            'https://picsum.photos/1600/900?random=5003',
            'https://picsum.photos/1600/900?random=5004',
            'https://picsum.photos/1600/900?random=5005'
        ]
    }
};

// 模态框元素
const modal = document.getElementById('galleryModal');
const modalTitle = document.getElementById('modalTitle');
const galleryImage = document.getElementById('galleryImage');
const galleryIndicators = document.getElementById('galleryIndicators');
const closeBtn = document.querySelector('.close');
const galleryPrev = document.getElementById('galleryPrev');
const galleryNext = document.getElementById('galleryNext');

let currentGallery = null;
let currentImageIndex = 0;
let currentGalleryIndex = -1; // 记录当前画廊的分类索引

// 打开画廊
function openGallery(galleryIndex) {
    currentGalleryIndex = galleryIndex;
    
    // 优先使用缓存的Unsplash图片，否则使用默认图片
    if (imageCache.galleries[galleryIndex] && imageCache.galleries[galleryIndex].length > 0) {
        currentGallery = {
            title: galleries[galleryIndex].title,
            images: imageCache.galleries[galleryIndex],
            categoryId: galleryIndex
        };
    } else {
        currentGallery = {
            ...galleries[galleryIndex],
            categoryId: galleryIndex
        };
    }
    
    currentImageIndex = 0;
    modal.style.display = 'block';
    modalTitle.textContent = currentGallery.title;
    updateGalleryImage();
    createIndicators();
}

// 更新画廊图片
function updateGalleryImage() {
    // 显示加载中状态
    galleryImage.style.opacity = '0.5';
    galleryImage.src = currentGallery.images[currentImageIndex];
    
    // 图片加载完成
    galleryImage.onload = () => {
        galleryImage.style.opacity = '1';
        // 更新统计显示
        setTimeout(() => {
            updateStatsDisplay();
        }, 100);
    };
    
    // 图片加载错误处理
    galleryImage.onerror = () => {
        galleryImage.src = 'https://via.placeholder.com/1600x900?text=Image+Not+Available';
        galleryImage.style.opacity = '1';
        // 更新统计显示
        setTimeout(() => {
            updateStatsDisplay();
        }, 100);
    };
    
    updateIndicators();
}

// 创建指示器
function createIndicators() {
    galleryIndicators.innerHTML = '';
    currentGallery.images.forEach((_, index) => {
        const indicator = document.createElement('div');
        indicator.classList.add('indicator');
        if (index === currentImageIndex) {
            indicator.classList.add('active');
        }
        indicator.addEventListener('click', () => {
            currentImageIndex = index;
            updateGalleryImage();
        });
        galleryIndicators.appendChild(indicator);
    });
}

// 更新指示器
function updateIndicators() {
    const indicators = galleryIndicators.querySelectorAll('.indicator');
    indicators.forEach((indicator, index) => {
        if (index === currentImageIndex) {
            indicator.classList.add('active');
        } else {
            indicator.classList.remove('active');
        }
    });
}

// 检查是否需要加载更多图片
async function checkAndLoadMoreImages() {
    if (currentGalleryIndex === -1) return;
    
    const totalImages = currentGallery.images.length;
    const remainingImages = totalImages - currentImageIndex;
    
    // 当剩余图片少于5张时，加载更多图片
    if (remainingImages <= 5 && !imageCache.loading[currentGalleryIndex]) {
        console.log(`剩余 ${remainingImages} 张图片，开始加载更多...`);
        
        const newImagesCount = await loadMoreImagesForCategory(currentGalleryIndex);
        
        if (newImagesCount > 0) {
            // 更新当前画廊的图片数组
            currentGallery.images = [...imageCache.galleries[currentGalleryIndex]];
            
            // 重新创建指示器
            createIndicators();
            
            console.log(`成功加载 ${newImagesCount} 张新图片，总计 ${currentGallery.images.length} 张`);
        }
    }
}

// 画廊导航 - 支持无限加载
galleryPrev.addEventListener('click', () => {
    currentImageIndex = (currentImageIndex - 1 + currentGallery.images.length) % currentGallery.images.length;
    updateGalleryImage();
});

galleryNext.addEventListener('click', async () => {
    currentImageIndex = (currentImageIndex + 1) % currentGallery.images.length;
    updateGalleryImage();
    
    // 检查是否需要加载更多图片
    await checkAndLoadMoreImages();
});

// 关闭模态框
closeBtn.addEventListener('click', () => {
    modal.style.display = 'none';
});

// 点击外部关闭
window.addEventListener('click', (e) => {
    if (e.target === modal) {
        modal.style.display = 'none';
    }
});

// ESC键关闭
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && modal.style.display === 'block') {
        modal.style.display = 'none';
    }
});

// 移动端触摸检测
let isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;

// 移动端卡片翻转状态
let flippedCards = new Set();

// 卡片点击事件
cards.forEach((cardWrapper, index) => {
    const card = cardWrapper.querySelector('.card');
    
    // 移动端双击翻转卡片
    if (isMobile) {
        let tapCount = 0;
        let tapTimer;
        
        card.addEventListener('touchstart', (e) => {
            tapCount++;
            
            if (tapCount === 1) {
                tapTimer = setTimeout(() => {
                    // 单击处理
                    if (index === currentIndex) {
                        if (flippedCards.has(index)) {
                            // 如果已翻转且是当前卡片，打开画廊
                            openGallery(index);
                        } else {
                            // 翻转卡片
                            cardWrapper.classList.add('active');
                            flippedCards.add(index);
                        }
                    } else {
                        // 切换到该卡片并清除其他翻转状态
                        cards.forEach((wrapper, i) => {
                            wrapper.classList.remove('active');
                            flippedCards.delete(i);
                        });
                        currentIndex = index;
                        rotateCarousel();
                    }
                    tapCount = 0;
                }, 300);
            } else if (tapCount === 2) {
                // 双击处理
                clearTimeout(tapTimer);
                if (index === currentIndex) {
                    openGallery(index);
                }
                tapCount = 0;
            }
        });
        
        // 防止默认触摸行为
        card.addEventListener('touchmove', (e) => {
            e.preventDefault();
        });
    } else {
        // 桌面端点击事件
        card.addEventListener('click', (e) => {
            e.stopPropagation();
            if (index === currentIndex) {
                // 如果点击的是当前卡片，打开画廊
                openGallery(index);
            } else {
                // 否则切换到该卡片
                currentIndex = index;
                rotateCarousel();
            }
        });
    }
});

// 移动端viewport适配
function updateViewport() {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
}

// 初始设置和窗口变化时更新
window.addEventListener('resize', () => {
    updateViewport();
    // 检测是否变为移动端
    isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
});

// 初始化viewport
updateViewport();

// ========================= 新增功能 =========================

// 获取当前图片的唯一标识符
function getCurrentImageId() {
    const currentImageUrl = currentGallery.images[currentImageIndex];
    return `${currentGalleryIndex}_${currentImageIndex}_${btoa(currentImageUrl).slice(0, 10)}`;
}

// 初始化图片统计数据
function initImageStats(imageId) {
    if (!imageStats.likes[imageId]) imageStats.likes[imageId] = 0;
    if (!imageStats.dislikes[imageId]) imageStats.dislikes[imageId] = 0;
    if (!imageStats.downloads[imageId]) imageStats.downloads[imageId] = 0;
    if (!imageStats.userActions[imageId]) imageStats.userActions[imageId] = {};
}

// 更新统计显示
function updateStatsDisplay() {
    const imageId = getCurrentImageId();
    initImageStats(imageId);
    
    const likeBtn = document.getElementById('likeBtn');
    const dislikeBtn = document.getElementById('dislikeBtn');
    const likeCount = document.getElementById('likeCount');
    const dislikeCount = document.getElementById('dislikeCount');
    const downloadCount = document.getElementById('downloadCount');
    
    // 更新数量显示
    likeCount.textContent = imageStats.likes[imageId];
    dislikeCount.textContent = imageStats.dislikes[imageId];
    downloadCount.textContent = imageStats.downloads[imageId];
    
    // 更新按钮状态
    likeBtn.classList.toggle('active', imageStats.userActions[imageId].liked === true);
    dislikeBtn.classList.toggle('active', imageStats.userActions[imageId].disliked === true);
}

// 点赞功能
function likeImage() {
    const imageId = getCurrentImageId();
    initImageStats(imageId);
    
    if (imageStats.userActions[imageId].liked) {
        // 取消点赞
        imageStats.likes[imageId]--;
        imageStats.userActions[imageId].liked = false;
    } else {
        // 点赞
        imageStats.likes[imageId]++;
        imageStats.userActions[imageId].liked = true;
        
        // 如果之前点踩了，取消点踩
        if (imageStats.userActions[imageId].disliked) {
            imageStats.dislikes[imageId]--;
            imageStats.userActions[imageId].disliked = false;
        }
    }
    
    updateStatsDisplay();
    
    // 保存到本地存储
    localStorage.setItem('imageStats', JSON.stringify(imageStats));
}

// 点踩功能
function dislikeImage() {
    const imageId = getCurrentImageId();
    initImageStats(imageId);
    
    if (imageStats.userActions[imageId].disliked) {
        // 取消点踩
        imageStats.dislikes[imageId]--;
        imageStats.userActions[imageId].disliked = false;
    } else {
        // 点踩
        imageStats.dislikes[imageId]++;
        imageStats.userActions[imageId].disliked = true;
        
        // 如果之前点赞了，取消点赞
        if (imageStats.userActions[imageId].liked) {
            imageStats.likes[imageId]--;
            imageStats.userActions[imageId].liked = false;
        }
    }
    
    updateStatsDisplay();
    
    // 保存到本地存储
    localStorage.setItem('imageStats', JSON.stringify(imageStats));
}

// 下载图片功能
async function downloadImage() {
    const imageUrl = currentGallery.images[currentImageIndex];
    const imageId = getCurrentImageId();
    
    try {
        // 获取图片数据
        const response = await fetch(imageUrl);
        const blob = await response.blob();
        
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `beauty-${currentGalleryIndex}-${currentImageIndex}-${Date.now()}.jpg`;
        
        // 触发下载
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        // 更新下载统计
        initImageStats(imageId);
        imageStats.downloads[imageId]++;
        updateStatsDisplay();
        
        // 保存到本地存储
        localStorage.setItem('imageStats', JSON.stringify(imageStats));
        
        // 显示成功消息
        showNotification('图片下载成功！', 'success');
        
    } catch (error) {
        console.error('下载失败:', error);
        showNotification('下载失败，请稍后重试', 'error');
    }
}

// 设置为桌面壁纸功能（仅在支持的浏览器中有效）
function setAsWallpaper() {
    const imageUrl = currentGallery.images[currentImageIndex];
    
    // 由于浏览器安全限制，无法直接设置桌面壁纸
    // 这里提供下载功能并给出提示
    downloadImage().then(() => {
        showNotification('图片已下载，请手动设置为桌面壁纸', 'info');
    });
}

// 全屏查看功能
let currentZoom = 1;
let isDragging = false;
let startX, startY, translateX = 0, translateY = 0;

function viewFullSize() {
    const fullscreenViewer = document.getElementById('fullscreenViewer');
    const fullscreenImage = document.getElementById('fullscreenImage');
    
    fullscreenImage.src = currentGallery.images[currentImageIndex];
    fullscreenViewer.style.display = 'block';
    
    // 重置缩放和位置
    currentZoom = 1;
    translateX = 0;
    translateY = 0;
    updateImageTransform();
}

// 更新图片变换
function updateImageTransform() {
    const fullscreenImage = document.getElementById('fullscreenImage');
    fullscreenImage.style.transform = `translate(${translateX}px, ${translateY}px) scale(${currentZoom})`;
}

// 缩放功能
function zoomIn() {
    currentZoom = Math.min(currentZoom * 1.2, 5);
    updateImageTransform();
}

function zoomOut() {
    currentZoom = Math.max(currentZoom / 1.2, 0.1);
    updateImageTransform();
}

function resetZoom() {
    currentZoom = 1;
    translateX = 0;
    translateY = 0;
    updateImageTransform();
}

// 通知功能
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 样式
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '5px',
        color: 'white',
        fontSize: '14px',
        zIndex: '3000',
        opacity: '0',
        transform: 'translateX(100%)',
        transition: 'all 0.3s ease'
    });
    
    // 根据类型设置背景色
    switch(type) {
        case 'success':
            notification.style.backgroundColor = '#4CAF50';
            break;
        case 'error':
            notification.style.backgroundColor = '#f44336';
            break;
        case 'info':
        default:
            notification.style.backgroundColor = '#2196F3';
            break;
    }
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动移除
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 从本地存储加载统计数据
function loadStats() {
    try {
        const savedStats = localStorage.getItem('imageStats');
        if (savedStats) {
            const parsed = JSON.parse(savedStats);
            Object.assign(imageStats, parsed);
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

// ========================= 事件绑定 =========================

// 工具栏按钮事件
document.getElementById('viewFullSize').addEventListener('click', viewFullSize);
document.getElementById('setWallpaper').addEventListener('click', setAsWallpaper);
document.getElementById('downloadImage').addEventListener('click', downloadImage);

// 评价按钮事件
document.getElementById('likeBtn').addEventListener('click', likeImage);
document.getElementById('dislikeBtn').addEventListener('click', dislikeImage);

// 全屏查看器事件
document.getElementById('fullscreenClose').addEventListener('click', () => {
    document.getElementById('fullscreenViewer').style.display = 'none';
});

document.getElementById('zoomIn').addEventListener('click', zoomIn);
document.getElementById('zoomOut').addEventListener('click', zoomOut);
document.getElementById('resetZoom').addEventListener('click', resetZoom);

// 全屏查看器鼠标拖拽
const fullscreenImage = document.getElementById('fullscreenImage');

fullscreenImage.addEventListener('mousedown', (e) => {
    if (currentZoom > 1) {
        isDragging = true;
        startX = e.clientX - translateX;
        startY = e.clientY - translateY;
        fullscreenImage.style.cursor = 'grabbing';
    }
});

document.addEventListener('mousemove', (e) => {
    if (isDragging && currentZoom > 1) {
        translateX = e.clientX - startX;
        translateY = e.clientY - startY;
        updateImageTransform();
    }
});

document.addEventListener('mouseup', () => {
    isDragging = false;
    fullscreenImage.style.cursor = 'move';
});

// 鼠标滚轮缩放
document.getElementById('fullscreenViewer').addEventListener('wheel', (e) => {
    e.preventDefault();
    if (e.deltaY < 0) {
        zoomIn();
    } else {
        zoomOut();
    }
});

// ESC键关闭全屏查看器
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        const fullscreenViewer = document.getElementById('fullscreenViewer');
        if (fullscreenViewer.style.display === 'block') {
            fullscreenViewer.style.display = 'none';
        }
    }
});

// 点击全屏背景关闭
document.getElementById('fullscreenViewer').addEventListener('click', (e) => {
    if (e.target.id === 'fullscreenViewer') {
        document.getElementById('fullscreenViewer').style.display = 'none';
    }
});

// 修改原有的updateGalleryImage函数，添加统计显示更新
const originalUpdateGalleryImage = updateGalleryImage;
updateGalleryImage = function() {
    originalUpdateGalleryImage.call(this);
    // 更新统计显示
    setTimeout(() => {
        updateStatsDisplay();
    }, 100);
};

// 初始化
loadStats();

console.log('增强功能已加载：查看原图、设置壁纸、下载图片、点赞点踩、下载统计');
