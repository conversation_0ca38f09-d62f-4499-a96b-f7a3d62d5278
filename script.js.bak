// 获取元素
const card = document.getElementById('card');
const particlesContainer = document.getElementById('particles');

// 交互式震动效果
card.addEventListener('click', () => {
    card.classList.add('shake');
    setTimeout(() => card.classList.remove('shake'), 500);
});

// 生成粒子
function createParticle() {
    const particle = document.createElement('div');
    particle.classList.add('particle');
    const size = Math.random() * 5 + 2;
    particle.style.width = `${size}px`;
    particle.style.height = `${size}px`;
    particle.style.left = `${Math.random() * 100}%`;
    particle.style.animationDuration = `${Math.random() * 10 + 5}s`;
    particlesContainer.appendChild(particle);
    
    // 移除粒子以防止内存泄漏
    setTimeout(() => particle.remove(), 20000);
}

// 创建多个粒子
function generateParticles() {
    for (let i = 0; i < 100; i++) {
        setTimeout(createParticle, i * 200);
    }
}

generateParticles();
