/* CSS变量定义 */
:root {
    --primary-color: #00d4ff;
    --secondary-color: #ff00ff;
    --tertiary-color: #ffff00;
    --card-width: 320px;
    --card-height: 450px;
    --glow-color: rgba(0, 212, 255, 0.5);
    --carousel-radius: 350px;
}

/* 移动端CSS变量 */
@media (max-width: 768px) {
    :root {
        --card-width: 280px;
        --card-height: 380px;
        --carousel-radius: 250px;
    }
}

@media (max-width: 480px) {
    :root {
        --card-width: 260px;
        --card-height: 340px;
        --carousel-radius: 200px;
    }
}

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #0a0a0a;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 轮播容器 */
.carousel-container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(ellipse at center, #1a1a2e 0%, #0a0a0a 100%);
    perspective: 1200px;
}

/* 轮播主体 */
.carousel {
    position: relative;
    width: 100%;
    height: var(--card-height);
    transform-style: preserve-3d;
    transition: transform 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 卡片包装器 */
.card-wrapper {
    position: absolute;
    width: var(--card-width);
    height: var(--card-height);
    left: 50%;
    margin-left: calc(var(--card-width) / -2);
    transform-style: preserve-3d;
    transition: transform 0.8s, opacity 0.8s;
}

/* 卡片主体 */
.card {
    width: var(--card-width);
    height: var(--card-height);
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 3D翻转效果 */
.card-wrapper:hover .card {
    transform: rotateY(180deg);
}

/* 各卡片主题 */
/* 清纯型 - 淡蓝色/白色系 */
.card-wrapper:nth-child(1) .card-front {
    background-image: linear-gradient(rgba(135, 206, 250, 0.6), rgba(176, 224, 230, 0.6));
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.card-wrapper:nth-child(1) .card-back {
    background: linear-gradient(135deg, #87CEEB 0%, #B0E0E6 100%);
}

/* 性感型 - 红色/黑色系 */
.card-wrapper:nth-child(2) .card-front {
    background-image: linear-gradient(rgba(220, 20, 60, 0.7), rgba(139, 0, 0, 0.7));
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.card-wrapper:nth-child(2) .card-back {
    background: linear-gradient(135deg, #DC143C 0%, #8B0000 100%);
}

/* 可爱型 - 粉色/浅色系 */
.card-wrapper:nth-child(3) .card-front {
    background-image: linear-gradient(rgba(255, 182, 193, 0.6), rgba(255, 228, 225, 0.6));
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.card-wrapper:nth-child(3) .card-back {
    background: linear-gradient(135deg, #FFB6C1 0%, #FFE4E1 100%);
}

/* 气质型 - 紫色/灰色系 */
.card-wrapper:nth-child(4) .card-front {
    background-image: linear-gradient(rgba(138, 43, 226, 0.6), rgba(147, 112, 219, 0.6));
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.card-wrapper:nth-child(4) .card-back {
    background: linear-gradient(135deg, #8A2BE2 0%, #9370DB 100%);
}

/* 古典型 - 金色/棕色系 */
.card-wrapper:nth-child(5) .card-front {
    background-image: linear-gradient(rgba(218, 165, 32, 0.6), rgba(205, 133, 63, 0.6));
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}
.card-wrapper:nth-child(5) .card-back {
    background: linear-gradient(135deg, #DAA520 0%, #CD853F 100%);
}

/* 卡片面 */
.card-face {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* 卡片正面 */
.card-front {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: rotateY(0deg);
}

/* 卡片背面 */
.card-back {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    transform: rotateY(180deg);
}

/* 光晕效果 */
.card-glow {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, transparent 30%, var(--glow-color) 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.card:hover .card-glow {
    opacity: 1;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* 卡片内容 */
.card-content {
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    z-index: 2;
}

/* 正面样式 */
.card-title {
    font-size: 2.5em;
    font-weight: bold;
    color: #fff;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px var(--primary-color);
    }
    to {
        text-shadow: 0 0 20px #fff, 0 0 30px var(--secondary-color), 0 0 40px var(--secondary-color);
    }
}

.card-divider {
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #fff, transparent);
    margin: 0 auto 20px;
    animation: scan 3s linear infinite;
}

@keyframes scan {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.card-description {
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    font-size: 1.1em;
    margin-bottom: 30px;
}

.card-stats {
    display: flex;
    justify-content: space-around;
    margin-top: auto;
}

.stat {
    text-align: center;
    color: #fff;
}

.stat-value {
    display: block;
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9em;
    opacity: 0.8;
}

/* 背面样式 */
.back-title {
    font-size: 2em;
    color: #fff;
    text-align: center;
    margin-bottom: 30px;
}

.feature-list {
    list-style: none;
    color: #fff;
    font-size: 1.1em;
    flex-grow: 1;
}

.feature-list li {
    margin-bottom: 15px;
    padding-left: 25px;
    position: relative;
}

.feature-list li::before {
    content: '✦';
    position: absolute;
    left: 0;
    color: var(--tertiary-color);
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.back-footer {
    display: flex;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9em;
    margin-top: auto;
}

/* 震动动画 */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

/* 导航按钮 */
.navigation {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    transform: translateY(-50%);
    z-index: 3;
}

.nav-prev, .nav-next {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    padding: 10px;
    color: white;
    cursor: pointer;
    font-size: 18px;
    transition: background-color 0.3s ease;
}

.nav-prev:hover, .nav-next:hover {
    background: rgba(0, 0, 0, 0.8);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.9);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    position: relative;
    margin: 2% auto;
    width: 90%;
    max-width: 1200px;
    background-color: #1a1a2e;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
}

.close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s;
}

.close:hover {
    color: var(--primary-color);
}

.modal-title {
    color: #fff;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5em;
}

/* 画廊容器 */
.gallery-container {
    position: relative;
    width: 100%;
    height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.5s ease;
}

@keyframes slideIn {
    from {
        transform: translateX(100px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 画廊导航 */
.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    font-size: 3em;
    padding: 10px 20px;
    cursor: pointer;
    transition: background-color 0.3s;
    border-radius: 5px;
}

.gallery-prev {
    left: 20px;
}

.gallery-next {
    right: 20px;
}

.gallery-nav:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

/* 指示器 */
.gallery-indicators {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 10px;
}

.indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: background-color 0.3s;
}

.indicator.active {
    background-color: var(--primary-color);
}

/* 图片工具栏 */
.image-toolbar {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.toolbar-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    white-space: nowrap;
}

.toolbar-btn:hover {
    background: var(--primary-color);
    transform: translateY(-2px);
}

.toolbar-btn svg {
    flex-shrink: 0;
}

/* 图片统计和评价 */
.image-stats {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px 20px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.rating-section {
    display: flex;
    gap: 15px;
}

.rating-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.like-btn:hover {
    background: #4CAF50;
    color: white;
}

.like-btn.active {
    background: #4CAF50;
    color: white;
}

.dislike-btn:hover {
    background: #f44336;
    color: white;
}

.dislike-btn.active {
    background: #f44336;
    color: white;
}

.download-stats {
    color: #ccc;
    font-size: 14px;
}

.stats-label {
    margin-right: 5px;
}

/* 全屏查看器 */
.fullscreen-viewer {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    z-index: 2000;
    overflow: hidden;
}

.fullscreen-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fullscreen-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
    cursor: move;
}

.fullscreen-close {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    background: none;
    border: none;
    z-index: 2001;
    transition: color 0.3s;
}

.fullscreen-close:hover {
    color: var(--primary-color);
}

.fullscreen-controls {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.fullscreen-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    font-weight: bold;
}

.fullscreen-btn:hover {
    background: var(--primary-color);
    transform: scale(1.1);
}

/* 移动端优化 - 平板和PC */
@media (max-width: 1024px) {
    .carousel-container {
        perspective: 1000px;
    }
    
    .card-content {
        padding: 25px;
    }
    
    .card-title {
        font-size: 2.2em;
    }
    
    .card-description {
        font-size: 1em;
    }
}

/* 移动端优化 - 大屏手机/小平板 */
@media (max-width: 768px) {
    body {
        overflow-x: hidden;
        overflow-y: auto;
    }
    
    .carousel-container {
        perspective: 800px;
        padding: 20px 10px;
        height: 100vh;
        overflow: visible;
    }
    
    .carousel {
        transform: translateZ(-50px);
    }
    
    .card-wrapper {
        transform: translateZ(var(--carousel-radius)) rotateY(calc(var(--angle) * 1deg));
    }
    
    .card-content {
        padding: 20px;
    }
    
    .card-title {
        font-size: 2em;
        margin-bottom: 15px;
    }
    
    .card-description {
        font-size: 0.95em;
        margin-bottom: 20px;
    }
    
    .back-title {
        font-size: 1.7em;
        margin-bottom: 20px;
    }
    
    .feature-list {
        font-size: 1em;
    }
    
    .feature-list li {
        margin-bottom: 12px;
        padding-left: 20px;
    }
    
    /* 导航按钮优化 */
    .nav-prev, .nav-next {
        padding: 15px 20px;
        font-size: 20px;
        border-radius: 10px;
        min-height: 50px;
        min-width: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* 模态框优化 */
    .modal-content {
        width: 95%;
        margin: 5px auto;
        padding: 15px;
        border-radius: 15px;
        max-height: 95vh;
        overflow-y: auto;
    }
    
    .modal-title {
        font-size: 2em;
        margin-bottom: 20px;
    }
    
    .gallery-container {
        height: 60vh;
        min-height: 300px;
    }
    
    .gallery-nav {
        font-size: 2.5em;
        padding: 8px 15px;
        min-width: 50px;
        min-height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .gallery-prev {
        left: 10px;
    }
    
    .gallery-next {
        right: 10px;
    }
    
    /* 工具栏优化 */
    .image-toolbar {
        top: 10px;
        right: 10px;
        padding: 10px;
        flex-direction: row;
        gap: 8px;
        border-radius: 8px;
    }
    
    .toolbar-btn {
        padding: 10px 12px;
        font-size: 12px;
        border-radius: 6px;
        min-width: 44px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .toolbar-btn svg {
        width: 18px;
        height: 18px;
    }
    
    /* 统计面板优化 */
    .image-stats {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 12px 15px;
        border-radius: 8px;
    }
    
    .rating-btn {
        padding: 10px 12px;
        font-size: 13px;
        min-width: 44px;
        min-height: 44px;
    }
    
    .rating-btn svg {
        width: 18px;
        height: 18px;
    }
    
    /* 全屏查看器优化 */
    .fullscreen-close {
        top: 15px;
        right: 20px;
        font-size: 30px;
        min-width: 50px;
        min-height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
    }
    
    .fullscreen-controls {
        bottom: 20px;
        padding: 10px 15px;
        border-radius: 20px;
        gap: 8px;
    }
    
    .fullscreen-btn {
        padding: 10px 15px;
        font-size: 14px;
        min-width: 44px;
        min-height: 44px;
        border-radius: 8px;
    }
}

/* 小屏手机优化 */
@media (max-width: 480px) {
    .carousel-container {
        perspective: 600px;
        padding: 15px 5px;
    }
    
    .card-content {
        padding: 15px;
    }
    
    .card-title {
        font-size: 1.8em;
        margin-bottom: 10px;
    }
    
    .card-description {
        font-size: 0.9em;
        margin-bottom: 15px;
    }
    
    .back-title {
        font-size: 1.5em;
        margin-bottom: 15px;
    }
    
    .feature-list {
        font-size: 0.9em;
    }
    
    .feature-list li {
        margin-bottom: 10px;
        padding-left: 18px;
    }
    
    /* 导航按钮 */
    .nav-prev, .nav-next {
        padding: 12px 15px;
        font-size: 18px;
        margin: 0 5px;
    }
    
    /* 模态框 */
    .modal-content {
        width: 98%;
        margin: 2px auto;
        padding: 10px;
        border-radius: 10px;
        max-height: 98vh;
    }
    
    .close {
        top: 10px;
        right: 15px;
        font-size: 30px;
    }
    
    .modal-title {
        font-size: 1.6em;
        margin-bottom: 15px;
    }
    
    .gallery-container {
        height: 50vh;
        min-height: 250px;
        flex-direction: column;
        justify-content: flex-start;
        padding: 10px 0;
    }
    
    .gallery-image {
        max-height: calc(50vh - 120px);
        border-radius: 8px;
    }
    
    .gallery-nav {
        font-size: 2em;
        padding: 6px 12px;
        min-width: 44px;
        min-height: 44px;
        border-radius: 8px;
    }
    
    .gallery-prev {
        left: 5px;
    }
    
    .gallery-next {
        right: 5px;
    }
    
    /* 工具栏 - 移动端重新设计 */
    .image-toolbar {
        position: static;
        order: -1;
        margin: 0 0 10px 0;
        padding: 8px;
        flex-direction: row;
        justify-content: space-around;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 10px;
    }
    
    .toolbar-btn {
        flex: 1;
        margin: 0 2px;
        padding: 8px;
        font-size: 10px;
        text-align: center;
        border-radius: 6px;
        min-width: 0;
    }
    
    .toolbar-btn svg {
        width: 16px;
        height: 16px;
        margin: 0 auto 2px;
        display: block;
    }
    
    .toolbar-btn span {
        display: block;
        font-size: 9px;
        line-height: 1;
    }
    
    /* 统计面板 */
    .image-stats {
        position: static;
        order: 1;
        margin: 10px 0 0 0;
        padding: 10px;
        flex-direction: column;
        gap: 8px;
        border-radius: 10px;
    }
    
    .rating-section {
        justify-content: center;
        gap: 10px;
    }
    
    .rating-btn {
        padding: 8px 12px;
        font-size: 12px;
        min-width: 44px;
        min-height: 36px;
    }
    
    .rating-btn svg {
        width: 16px;
        height: 16px;
    }
    
    .download-stats {
        text-align: center;
        font-size: 12px;
    }
    
    /* 指示器 */
    .gallery-indicators {
        margin-top: 10px;
        gap: 6px;
        flex-wrap: wrap;
        justify-content: center;
        max-height: 60px;
        overflow-y: auto;
    }
    
    .indicator {
        width: 8px;
        height: 8px;
        margin: 1px;
    }
    
    /* 全屏查看器 */
    .fullscreen-close {
        top: 10px;
        right: 15px;
        font-size: 24px;
        min-width: 40px;
        min-height: 40px;
    }
    
    .fullscreen-controls {
        bottom: 15px;
        padding: 8px 12px;
        gap: 6px;
    }
    
    .fullscreen-btn {
        padding: 8px 12px;
        font-size: 12px;
        min-width: 40px;
        min-height: 36px;
        border-radius: 6px;
    }
    
    /* 重新排列画廊内容 */
    .gallery-container {
        display: flex;
        flex-direction: column;
    }
    
    .gallery-container .gallery-image {
        order: 0;
    }
    
    .gallery-container .image-toolbar {
        order: -1;
    }
    
    .gallery-container .image-stats {
        order: 1;
    }
}

/* 超小屏手机 */
@media (max-width: 360px) {
    .card-content {
        padding: 12px;
    }
    
    .card-title {
        font-size: 1.6em;
    }
    
    .card-description {
        font-size: 0.85em;
    }
    
    .toolbar-btn {
        padding: 6px;
        font-size: 9px;
    }
    
    .toolbar-btn svg {
        width: 14px;
        height: 14px;
    }
    
    .rating-btn {
        padding: 6px 8px;
        font-size: 11px;
        min-width: 36px;
        min-height: 32px;
    }
    
    .modal-title {
        font-size: 1.4em;
    }
    
    .gallery-nav {
        font-size: 1.8em;
        padding: 4px 8px;
    }
}

/* 横屏适配 */
@media (max-height: 600px) and (orientation: landscape) {
    .carousel-container {
        height: 100vh;
        padding: 10px;
    }
    
    .gallery-container {
        height: 70vh;
    }
    
    .modal-content {
        max-height: 95vh;
        overflow-y: auto;
    }
    
    .image-toolbar {
        padding: 6px;
    }
    
    .toolbar-btn {
        padding: 6px 8px;
        font-size: 10px;
    }
    
    .image-stats {
        padding: 8px 12px;
    }
}

/* 触摸优化 */
@media (hover: none) and (pointer: coarse) {
    .card-wrapper:hover .card {
        transform: none;
    }
    
    .card-wrapper.active .card {
        transform: rotateY(180deg);
    }
    
    .toolbar-btn:hover,
    .rating-btn:hover,
    .gallery-nav:hover {
        transform: none;
    }
    
    .toolbar-btn:active,
    .rating-btn:active {
        transform: scale(0.95);
        background: var(--primary-color);
    }
    
    /* 增大点击区域 */
    .toolbar-btn,
    .rating-btn,
    .gallery-nav,
    .indicator {
        min-width: 44px;
        min-height: 44px;
    }
}
