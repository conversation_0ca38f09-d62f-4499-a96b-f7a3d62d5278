/* CSS变量定义 */
:root {
    --primary-color: #00d4ff;
    --secondary-color: #ff00ff;
    --tertiary-color: #ffff00;
    --card-width: 320px;
    --card-height: 450px;
    --glow-color: rgba(0, 212, 255, 0.5);
}

/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: #0a0a0a;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 主容器 */
.container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(ellipse at center, #1a1a2e 0%, #0a0a0a 100%);
}

/* 粒子背景 */
.particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
    animation: float 20s linear infinite;
}

@keyframes float {
    from {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    to {
        transform: translateY(-100vh) rotate(360deg);
        opacity: 0;
    }
}

/* 卡片容器 */
.card-container {
    perspective: 1000px;
    z-index: 10;
}

/* 卡片主体 */
.card {
    width: var(--card-width);
    height: var(--card-height);
    position: relative;
    transform-style: preserve-3d;
    transition: transform 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 3D翻转效果 */
.card-container:hover .card {
    transform: rotateY(180deg);
}

/* 卡片面 */
.card-face {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

/* 卡片正面 */
.card-front {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: rotateY(0deg);
}

/* 卡片背面 */
.card-back {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    transform: rotateY(180deg);
}

/* 光晕效果 */
.card-glow {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, transparent 30%, var(--glow-color) 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.card:hover .card-glow {
    opacity: 1;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* 卡片内容 */
.card-content {
    padding: 30px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    z-index: 2;
}

/* 正面样式 */
.card-title {
    font-size: 2.5em;
    font-weight: bold;
    color: #fff;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: 20px;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px var(--primary-color);
    }
    to {
        text-shadow: 0 0 20px #fff, 0 0 30px var(--secondary-color), 0 0 40px var(--secondary-color);
    }
}

.card-divider {
    width: 80%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #fff, transparent);
    margin: 0 auto 20px;
    animation: scan 3s linear infinite;
}

@keyframes scan {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.card-description {
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    font-size: 1.1em;
    margin-bottom: 30px;
}

.card-stats {
    display: flex;
    justify-content: space-around;
    margin-top: auto;
}

.stat {
    text-align: center;
    color: #fff;
}

.stat-value {
    display: block;
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9em;
    opacity: 0.8;
}

/* 背面样式 */
.back-title {
    font-size: 2em;
    color: #fff;
    text-align: center;
    margin-bottom: 30px;
}

.feature-list {
    list-style: none;
    color: #fff;
    font-size: 1.1em;
    flex-grow: 1;
}

.feature-list li {
    margin-bottom: 15px;
    padding-left: 25px;
    position: relative;
}

.feature-list li::before {
    content: '✦';
    position: absolute;
    left: 0;
    color: var(--tertiary-color);
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.back-footer {
    display: flex;
    justify-content: space-between;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9em;
    margin-top: auto;
}

/* 震动动画 */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

/* 响应式设计 */
@media (max-width: 480px) {
    :root {
        --card-width: 280px;
        --card-height: 400px;
    }
    
    .card-title {
        font-size: 2em;
    }
    
    .card-description {
        font-size: 1em;
    }
}
